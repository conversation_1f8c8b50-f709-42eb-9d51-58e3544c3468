<?php

namespace App\Services;

use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class OrderService
{
    public static function createOrder(array $data)
    {
        $user = User::where('line_user_id', $data['line_user_id'])->first();
        if (!$user) {
            throw new \Exception('User not found');
        }
        $orderNumber = 'ORD' . now()->format('YmdHis') . rand(1000, 9999);
        DB::beginTransaction();
        try {
            $order = Order::create([
                'order_number' => $orderNumber,
                'user_id' => $user->id,
                'vendor_id' => $data['vendor_id'],
                'status' => 'pending',
                'total_amount' => $data['total_amount'],
                'delivery_address' => $data['delivery_address'],
                'payment_method' => $data['payment_method'],
                'payment_status' => $data['payment_status'] ?? 'unpaid',
                'notes' => $data['notes'] ?? null,
                'ordered_at' => now(),
            ]);
            foreach ($data['order_items'] as $item) {
                $order->products()->attach($item['product_id'], [
                    'quantity' => $item['quantity'],
                    'price_per_unit' => $item['price_per_unit'],
                ]);
            }
            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
} 