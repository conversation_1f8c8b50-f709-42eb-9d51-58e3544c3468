<?php

namespace App\Filament\Resources\Products\RelationManagers;

use App\Filament\Resources\Products\ProductResource;
use Filament\Actions\CreateAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;

class ProductPricesRelationManager extends RelationManager
{
    protected static string $relationship = 'productPrices';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('vendor.name')->label('廠商'),
                TextColumn::make('price')->label('價格'),
                TextColumn::make('effective_from')->label('生效日'),
            ])
            ->headerActions([
                CreateAction::make()
                    ->form([
                        Hidden::make('product_id')->default(fn ($livewire) => $livewire->ownerRecord->id),
                        Select::make('vendor_id')
                            ->label('廠商')
                            ->options(\App\Models\Vendor::all()->pluck('name', 'id')->toArray())
                            ->required(),
                        TextInput::make('price')->label('價格')->numeric()->required(),
                        DatePicker::make('effective_from')->label('生效日')->required(),
                    ]),
            ]);
    }
}
