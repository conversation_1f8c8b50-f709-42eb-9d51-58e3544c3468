<?php

namespace App\Filament\Resources\Products\Schemas;

use Filament\Schemas\Schema;

class ProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                \Filament\Forms\Components\TextInput::make('name')
                    ->label('商品名稱')
                    ->required(),
                \Filament\Forms\Components\TextInput::make('sku')
                    ->label('商品貨號'),
                \Filament\Forms\Components\Textarea::make('description')
                    ->label('商品描述'),
                \Filament\Forms\Components\TextInput::make('image_url')
                    ->label('商品圖片URL'),
                \Filament\Forms\Components\Toggle::make('is_published')
                    ->label('是否上架')
                    ->default(true),
            ]);
    }
}
