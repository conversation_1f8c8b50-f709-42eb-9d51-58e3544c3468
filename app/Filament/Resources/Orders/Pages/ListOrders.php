<?php

namespace App\Filament\Resources\Orders\Pages;

use App\Filament\Resources\Orders\OrderResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }

    protected function getTableRowAttributes($record): array
    {
        return [
            'class' => match ($record->status) {
                'pending' => 'bg-red-500',
                'confirmed' => 'bg-red-500',
                'delivering' => 'bg-red-500',
                'completed' => 'bg-red-500',
                'cancelled' => 'bg-red-500',
                default => '',
            },
        ];
    }

    protected function getTableCellAttributes($record, $column): array
    {
        if ($column->getName() !== 'order_number') {
            return [];
        }
        return [
            'class' => match ($record->status) {
                'pending' => 'bg-pink-100',
                'confirmed' => 'bg-yellow-100',
                'delivering' => 'bg-green-100',
                'completed' => 'bg-green-300',
                'cancelled' => 'bg-red-200',
                default => '',
            },
        ];
    }
}
