<?php

namespace App\Filament\Resources\Orders\Schemas;

use Filament\Schemas\Schema;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DateTimePicker;

class OrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('order_number')->label('訂單編號')->required()->unique(),
                Select::make('user_id')->label('用戶')->options(\App\Models\User::all()->pluck('name', 'id'))->searchable()->required(),
                Select::make('vendor_id')->label('廠商')->options(\App\Models\Vendor::all()->pluck('name', 'id'))->searchable()->required(),
                Select::make('status')->label('訂單狀態')->options([
                    'pending' => '待處理',
                    'confirmed' => '已確認',
                    'delivering' => '配送中',
                    'completed' => '已完成',
                    'cancelled' => '已取消',
                ])->required(),
                TextInput::make('total_amount')->label('訂單總金額')->numeric()->required(),
                Textarea::make('delivery_address')->label('地址快照')->required(),
                TextInput::make('payment_method')->label('付款方式')->required(),
                Select::make('payment_status')->label('付款狀態')->options([
                    'unpaid' => '未付款',
                    'paid' => '已付款',
                ])->required(),
                Textarea::make('notes')->label('客戶備註'),
                DateTimePicker::make('ordered_at')->label('下單時間')->required(),
            ]);
    }
}
