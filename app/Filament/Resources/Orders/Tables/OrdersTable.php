<?php

namespace App\Filament\Resources\Orders\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\SelectColumn;
use Illuminate\Database\Eloquent\Builder;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_number')
                    ->label('訂單編號')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->extraAttributes(fn ($record) => [
                        'class' => match ($record->status) {
                            'pending' => 'bg-pink-100',
                            'confirmed' => 'bg-yellow-100',
                            'delivering' => 'bg-green-100',
                            'completed' => 'bg-green-300',
                            'cancelled' => 'bg-red-200',
                            default => '',
                        },
                    ]),
                TextColumn::make('user.name')
                    ->label('用戶')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('vendor.name')
                    ->label('廠商')
                    ->searchable()
                    ->toggleable(),
                SelectColumn::make('status')
                    ->label('訂單狀態')
                    ->options([
                        'pending' => '待處理',
                        'confirmed' => '已確認',
                        'delivering' => '配送中',
                        'completed' => '已完成',
                        'cancelled' => '已取消',
                    ])
                    ->selectablePlaceholder(false),
                TextColumn::make('total_amount')
                    ->label('訂單總金額')
                    ->money('TWD')
                    ->sortable(),
                TextColumn::make('delivery_address')
                    ->label('地址快照')
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('payment_method')
                    ->label('付款方式')
                    ->toggleable(),
                TextColumn::make('payment_status')
                    ->label('付款狀態')
                    ->badge()
                    ->color(fn($state) => match($state) {
                        'paid' => 'success',
                        'unpaid' => 'warning',
                        default => 'gray'
                    })
                    ->formatStateUsing(fn($state) => match($state) {
                        'paid' => '已付款',
                        'unpaid' => '未付款',
                        default => $state
                    }),
                TextColumn::make('notes')
                    ->label('客戶備註')
                    ->limit(20)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 20) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('ordered_at')
                    ->label('下單時間')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->label('建立時間')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label('更新時間')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TrashedFilter::make(),
                SelectFilter::make('status')
                    ->label('訂單狀態')
                    ->options([
                        'pending' => '待處理',
                        'confirmed' => '已確認',
                        'delivering' => '配送中',
                        'completed' => '已完成',
                        'cancelled' => '已取消',
                    ]),
                SelectFilter::make('payment_status')
                    ->label('付款狀態')
                    ->options([
                        'paid' => '已付款',
                        'unpaid' => '未付款',
                    ]),
                SelectFilter::make('vendor')
                    ->label('廠商')
                    ->relationship('vendor', 'name')
                    ->searchable()
                    ->preload(),
                Filter::make('ordered_at')
                    ->form([
                        DatePicker::make('ordered_from')
                            ->label('下單日期從'),
                        DatePicker::make('ordered_until')
                            ->label('下單日期到'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['ordered_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('ordered_at', '>=', $date),
                            )
                            ->when(
                                $data['ordered_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('ordered_at', '<=', $date),
                            );
                    }),
            ])
            ->defaultSort('ordered_at', 'desc')
            ->recordActions([
                EditAction::make(),
                ViewAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('沒有訂單')
            ->emptyStateDescription('目前沒有任何訂單記錄。')
            ->striped();
    }
}
