<?php

namespace App\Filament\Resources\Orders\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ForceDeleteBulkAction;
use Filament\Actions\RestoreBulkAction;
use Filament\Tables\Filters\TrashedFilter;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\DateTimeColumn;
use Filament\Tables\Columns\SelectColumn;

class OrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_number')
                    ->label('訂單編號')
                    ->searchable()
                    ->sortable()
                    ->extraAttributes(fn ($record) => [
                        'class' => match ($record->status) {
                            'pending' => 'bg-pink-100',
                            'confirmed' => 'bg-yellow-100',
                            'delivering' => 'bg-green-100',
                            'completed' => 'bg-green-300',
                            'cancelled' => 'bg-red-200',
                            default => '',
                        },
                    ]),
                TextColumn::make('user.name')->label('用戶')->searchable(),
                TextColumn::make('vendor.name')->label('廠商')->searchable(),
                SelectColumn::make('status')
                    ->label('訂單狀態')
                    ->options([
                        'pending' => '待處理',
                        'confirmed' => '已確認',
                        'delivering' => '配送中',
                        'completed' => '已完成',
                        'cancelled' => '已取消',
                    ]),
                TextColumn::make('total_amount')->label('訂單總金額')->money('TWD'),
                TextColumn::make('delivery_address')->label('地址快照')->limit(20),
                TextColumn::make('payment_method')->label('付款方式'),
                TextColumn::make('payment_status')->label('付款狀態')->badge()->color(fn($state) => $state === 'paid' ? 'success' : 'gray'),
                TextColumn::make('notes')->label('客戶備註')->limit(20),
                TextColumn::make('ordered_at')->label('下單時間')->dateTime()->sortable(),
                TextColumn::make('created_at')->label('建立時間')->dateTime()->sortable(),
                TextColumn::make('updated_at')->label('更新時間')->dateTime()->sortable(),
            ])
            ->filters([
                TrashedFilter::make(),
            ])

            ->recordActions([
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                    ForceDeleteBulkAction::make(),
                    RestoreBulkAction::make(),
                ]),
            ]);
    }
}
