<?php

namespace App\Filament\Resources\Vendors\RelationManagers;

use App\Filament\Resources\Vendors\VendorResource;
use Filament\Actions\CreateAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;

class ProductPricesRelationManager extends RelationManager
{
    protected static string $relationship = 'productPrices';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('product.name')->label('商品'),
                TextColumn::make('price')->label('價格'),
                TextColumn::make('effective_from')->label('生效日'),
            ])
            ->headerActions([
                CreateAction::make()
                    ->form([
                        Select::make('product_id')
                            ->label('商品')
                            ->options(\App\Models\Product::all()->pluck('name', 'id')->toArray())
                            ->required(),
                        TextInput::make('price')->label('價格')->numeric()->required(),
                        DatePicker::make('effective_from')->label('生效日')->required(),
                    ]),
            ]);
    }
}
