<?php

namespace App\Filament\Resources\Vendors\RelationManagers;

use App\Filament\Resources\Vendors\VendorResource;
use Filament\Actions\CreateAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\SelectColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;

class InventoriesRelationManager extends RelationManager
{
    protected static string $relationship = 'Inventories';

    // protected static ?string $relatedResource = VendorResource::class;

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                SelectColumn::make('product_id')
                    ->label('商品')
                    ->options(\App\Models\Product::all()->pluck('name', 'id')->toArray()),
                TextColumn::make('price')->label('價格'),
                TextColumn::make('quantity')->label('庫存'),
            ])
            ->headerActions([
                CreateAction::make()
                    ->form([
                        Select::make('product_id')
                            ->label('商品')
                            ->options(\App\Models\Product::all()->pluck('name', 'id')->toArray())
                            ->required(),
                        TextInput::make('price')->label('價格')->numeric()->required(),
                        TextInput::make('quantity')->label('庫存')->numeric()->required(),
                    ]),
            ]);
    }
}
