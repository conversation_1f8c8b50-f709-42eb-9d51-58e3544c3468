<?php

namespace App\Filament\Resources\Vendors\Schemas;

use Filament\Schemas\Schema;

class VendorForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                \Filament\Forms\Components\TextInput::make('name')
                    ->label('廠商/分店名稱')
                    ->required(),
                \Filament\Forms\Components\TextInput::make('address')
                    ->label('廠商地址'),
                \Filament\Forms\Components\TextInput::make('phone')
                    ->label('聯絡電話'),
                \Filament\Forms\Components\Toggle::make('is_active')
                    ->label('是否啟用')
                    ->default(true),
            ]);
    }
}
