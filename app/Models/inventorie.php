<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Inventorie extends Model
{
    protected $fillable = [
        'vendor_id',
        'product_id',
        'quantity',
        'low_stock_threshold',
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
