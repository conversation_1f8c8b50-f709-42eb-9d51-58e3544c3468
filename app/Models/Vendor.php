<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Vendor extends Model
{
    use HasFactory,SoftDeletes;

    protected $fillable = [
        'name',
        'address',
        'phone',
        'is_active',
    ];

    public function orders()
    {
        return $this->hasMany(order::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'inventories')->withPivot(['quantity', 'low_stock_threshold'])->withTimestamps();
    }

    public function inventories()
    {
        return $this->hasMany(\App\Models\Inventorie::class);
    }
    public function productPrices()
    {
        return $this->hasMany(\App\Models\ProductPrice::class);
    }
}
