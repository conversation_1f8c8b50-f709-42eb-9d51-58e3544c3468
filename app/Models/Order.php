<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'order_number',
        'user_id',
        'vendor_id',
        'status',
        'total_amount',
        'delivery_address',
        'payment_method',
        'payment_status',
        'notes',
        'ordered_at',
    ];

    protected $casts = [
        'ordered_at' => 'datetime',
        'total_amount' => 'decimal:2',
    ];

    protected $attributes = [
        'status' => 'pending',
        'payment_status' => 'unpaid',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'order_items')->withPivot(['quantity', 'price_per_unit'])->withTimestamps();
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    // 輔助方法
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'pending' => '待處理',
            'confirmed' => '已確認',
            'delivering' => '配送中',
            'completed' => '已完成',
            'cancelled' => '已取消',
            default => $this->status,
        };
    }

    public function getPaymentStatusLabelAttribute()
    {
        return match($this->payment_status) {
            'paid' => '已付款',
            'unpaid' => '未付款',
            default => $this->payment_status,
        };
    }
}
