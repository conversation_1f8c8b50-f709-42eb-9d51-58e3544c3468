<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'sku',
        'description',
        'image_url',
        'is_published',
    ];

    public function orders()
    {
        return $this->belongsToMany(Order::class, 'order_items')->withPivot(['quantity', 'price_per_unit'])->withTimestamps();
    }

    public function vendors()
    {
        return $this->belongsToMany(Vendor::class, 'inventories')->withPivot(['quantity', 'low_stock_threshold'])->withTimestamps();
    }

    public function productPrices()
    {
        return $this->hasMany(\App\Models\ProductPrice::class);
    }
    public function productPricesForVendor($vendorId)
    {
        return $this->hasMany(\App\Models\ProductPrice::class)
            ->where('vendor_id', $vendorId);
    }
}
