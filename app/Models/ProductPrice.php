<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductPrice extends Model
{
    protected $fillable = [
        'product_id',
        'vendor_id',
        'price',
        'effective_from',
    ];

    public function product()
    {
        return $this->belongsTo(\App\Models\Product::class);
    }

    public function vendor()
    {
        return $this->belongsTo(\App\Models\Vendor::class);
    }
}
