<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use LINE\Laravel\Facades\LINEMessagingApi;
use LINE\Clients\MessagingApi\Model\ReplyMessageRequest;
use LINE\Clients\MessagingApi\Model\TextMessage;
use LINE\Webhook\Model\MessageEvent;
use LINE\Parser\EventRequestParser;
use LINE\Constants\HTTPHeader;
use Illuminate\Support\Facades\DB;

class LineWebhookController extends Controller
{
    public function webhook(Request $request)
    {
        // 記錄收到的請求
        Log::info('LINE Webhook 收到請求', [
            'headers' => $request->headers->all(),
            'body' => $request->getContent()
        ]);

        $channelSecret = env('LINE_CHANNEL_SECRET');
        $signature = $request->header(HTTPHeader::LINE_SIGNATURE);
        $body = $request->getContent();
        
        // 檢查必要的參數
        if (empty($channelSecret)) {
            Log::error('LINE_CHANNEL_SECRET 未設定');
            return response('Server configuration error', 500);
        }
        
        if (empty($signature)) {
            Log::error('缺少 LINE 簽名標頭');
            return response('Missing signature', 400);
        }
        
        // 解析 webhook 事件（改用原生解析）
        $input = $request->getContent();
        $data = json_decode($input, true);
        Log::info('原生解析 events', ['events' => $data['events'] ?? []]);
        foreach (($data['events'] ?? []) as $event) {
            Log::info('原生 event', $event);
            // Postback 處理：直接訂購
            if (
                isset($event['type']) &&
                $event['type'] === 'postback' &&
                isset($event['postback']['data']) &&
                strpos($event['postback']['data'], 'action=reorder&orderId=') === 0 &&
                (empty($event['deliveryContext']['isRedelivery']) || $event['deliveryContext']['isRedelivery'] === false)
            ) {
                $replyToken = $event['replyToken'];
                parse_str($event['postback']['data'], $postbackData);
                $orderId = $postbackData['orderId'] ?? null;
                if ($orderId) {
                    $order = DB::table('orders')->where('id', $orderId)->first();
                    $orderItems = DB::table('order_items')->where('order_id', $orderId)->get();
                    if ($order && $orderItems->count()) {
                        $user = DB::table('users')->where('id', $order->user_id)->first();
                        $payload = [
                            'line_user_id' => $user->line_user_id,
                            'vendor_id' => $order->vendor_id,
                            'total_amount' => $order->total_amount,
                            'delivery_address' => $order->delivery_address,
                            'payment_method' => $order->payment_method,
                            'payment_status' => $order->payment_status,
                            'notes' => $order->notes,
                            'order_items' => $orderItems->map(function($item) {
                                return [
                                    'product_id' => $item->product_id,
                                    'quantity' => $item->quantity,
                                    'price_per_unit' => $item->price_per_unit,
                                ];
                            })->toArray(),
                        ];
                        Log::info('Webhook送出payload', $payload);
                        try {
                            $order = \App\Services\OrderService::createOrder($payload);
                            LINEMessagingApi::replyMessage(new ReplyMessageRequest([
                                'replyToken' => $replyToken,
                                'messages' => [
                                    ['type' => 'text', 'text' => '訂購成功訊息']
                                ]
                            ]));
                        } catch (\Exception $e) {
                            Log::error('訂單建立失敗: ' . $e->getMessage());
                            LINEMessagingApi::replyMessage(new ReplyMessageRequest([
                                'replyToken' => $replyToken,
                                'messages' => [
                                    ['type' => 'text', 'text' => '訂購失敗，請稍後再試。']
                                ]
                            ]));
                        }
                    }
                }
                continue;
            }
            if (
                isset($event['type'], $event['message']['type'], $event['message']['text']) &&
                $event['type'] === 'message' &&
                $event['message']['type'] === 'text' &&
                in_array($event['message']['text'], ['訂購瓦斯', '訂瓦斯', '送瓦斯'])
            ) {
                $replyToken = $event['replyToken'];
                $userId = $event['source']['userId'] ?? null;
                $displayName = '用戶';
                // 查詢用戶
                $user = DB::table('users')->where('line_user_id', $userId)->first();
                if ($user) {
                    $displayName = $user->display_name ?? '用戶';
                    // 查詢最新訂單
                    $order = DB::table('orders')
                        ->where('user_id', $user->id)
                        ->orderByDesc('created_at')
                        ->first();
                    if ($order) {
                        // 查詢商品明細
                        $items = DB::table('order_items')
                            ->join('products', 'order_items.product_id', '=', 'products.id')
                            ->where('order_items.order_id', $order->id)
                            ->select('products.name', 'order_items.quantity', 'order_items.price_per_unit')
                            ->get();
                        $productDetails = '';
                        foreach ($items as $item) {
                            $productDetails .= "{$item->name} x {$item->quantity} ({$item->price_per_unit}元)\n";
                        }
                        $productDetails = trim($productDetails);
                        // Flex Message for 熟客
                        $flex = [
                            "type" => "flex",
                            "altText" => "快速訂購",
                            "contents" => [
                                "type" => "bubble",
                                "hero" => [
                                    "type" => "image",
                                    "url" => "https://developers-resource.landpress.line.me/fx/img/01_1_cafe.png",
                                    "size" => "full",
                                    "aspectRatio" => "20:13",
                                    "aspectMode" => "cover",
                                    "action" => [
                                        "type" => "uri",
                                        "uri" => "https://line.me/"
                                    ]
                                ],
                                "body" => [
                                    "type" => "box",
                                    "layout" => "vertical",
                                    "contents" => [
                                        [
                                            "type" => "text",
                                            "text" => "確認訂購單",
                                            "weight" => "bold",
                                            "size" => "xl"
                                        ],
                                        [
                                            "type" => "box",
                                            "layout" => "vertical",
                                            "margin" => "lg",
                                            "spacing" => "sm",
                                            "contents" => [
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "spacing" => "sm",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "單價",
                                                            "color" => "#aaaaaa",
                                                            "size" => "sm",
                                                            "flex" => 1
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => isset($items[0]) ? (string)$items[0]->price_per_unit : '-',
                                                            "wrap" => true,
                                                            "color" => "#666666",
                                                            "size" => "sm",
                                                            "flex" => 5
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "spacing" => "sm",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "數量",
                                                            "color" => "#aaaaaa",
                                                            "size" => "sm",
                                                            "flex" => 1
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => isset($items[0]) ? (string)$items[0]->quantity : '-',
                                                            "wrap" => true,
                                                            "color" => "#666666",
                                                            "size" => "sm",
                                                            "flex" => 5
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "總計"
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => isset($order->total_amount) ? (string)$order->total_amount : '-',
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "運送地址"
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => $order->delivery_address ?? '-',
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "聯絡人"
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => $user->display_name ?? '-',
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "連絡電話"
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => $user->phone ?? '-',
                                                        ]
                                                    ]
                                                ],
                                                [
                                                    "type" => "box",
                                                    "layout" => "baseline",
                                                    "contents" => [
                                                        [
                                                            "type" => "text",
                                                            "text" => "付款方式"
                                                        ],
                                                        [
                                                            "type" => "text",
                                                            "text" => $order->payment_method ?? '-',
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ],
                                "footer" => [
                                    "type" => "box",
                                    "layout" => "vertical",
                                    "spacing" => "sm",
                                    "contents" => [
                                        [
                                            "type" => "button",
                                            "style" => "primary",
                                            "height" => "sm",
                                            "action" => [
                                                "type" => "postback",
                                                "label" => "確認",
                                                "data" => "action=reorder&orderId={$order->id}"
                                            ]
                                        ],
                                        [
                                            "type" => "button",
                                            "style" => "primary",
                                            "height" => "sm",
                                            "action" => [
                                                "type" => "uri",
                                                "label" => "另外選擇",
                                                "uri" => "https://liff.line.me/2004625704-jDdyYEnk/order?ts=" . time()
                                            ],
                                            "color" => "#dd6a1b"
                                        ],
                                        [
                                            "type" => "box",
                                            "layout" => "vertical",
                                            "contents" => [],
                                            "margin" => "sm"
                                        ]
                                    ],
                                    "flex" => 0
                                ]
                            ]
                        ];
                        LINEMessagingApi::replyMessage(new ReplyMessageRequest([
                            'replyToken' => $replyToken,
                            'messages' => [$flex]
                        ]));
                    } else {
                        // 沒有歷史訂單
                        $flex = [
                            "type" => "flex",
                            "altText" => "歡迎訂購瓦斯",
                            "contents" => [
                                "type" => "bubble",
                                "header" => [
                                    "type" => "box",
                                    "layout" => "vertical",
                                    "contents" => [
                                        [
                                            "type" => "text",
                                            "text" => "歡迎訂購瓦斯",
                                            "weight" => "bold",
                                            "size" => "lg"
                                        ]
                                    ]
                                ],
                                "body" => [
                                    "type" => "box",
                                    "layout" => "vertical",
                                    "contents" => [
                                        [
                                            "type" => "text",
                                            "text" => "您好，{$displayName}！歡迎使用我們的訂購服務，請點擊下方按鈕開始您的第一筆訂單。",
                                            "wrap" => true
                                        ]
                                    ]
                                ],
                                "footer" => [
                                    "type" => "box",
                                    "layout" => "vertical",
                                    "contents" => [
                                        [
                                            "type" => "button",
                                            "style" => "primary",
                                            "action" => [
                                                "type" => "uri",
                                                "label" => "點我開始訂購",
                                                "uri" => "https://liff.line.me/2004625704-jDdyYEnk/order?ts=" . time()
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ];
                        LINEMessagingApi::replyMessage(new ReplyMessageRequest([
                            'replyToken' => $replyToken,
                            'messages' => [$flex]
                        ]));
                    }
                } else {
                    // 用戶不存在，導向註冊頁
                    $flex = [
                        "type" => "flex",
                        "altText" => "歡迎訂購瓦斯",
                        "contents" => [
                            "type" => "bubble",
                            "header" => [
                                "type" => "box",
                                "layout" => "vertical",
                                "contents" => [
                                    [
                                        "type" => "text",
                                        "text" => "歡迎訂購瓦斯",
                                        "weight" => "bold",
                                        "size" => "lg"
                                    ]
                                ]
                            ],
                            "body" => [
                                "type" => "box",
                                "layout" => "vertical",
                                "contents" => [
                                    [
                                        "type" => "text",
                                        "text" => "您好，{$displayName}！請先註冊成為會員，才能開始訂購服務。",
                                        "wrap" => true
                                    ]
                                ]
                            ],
                            "footer" => [
                                "type" => "box",
                                "layout" => "vertical",
                                "contents" => [
                                    [
                                        "type" => "button",
                                        "style" => "primary",
                                        "action" => [
                                            "type" => "uri",
                                            "label" => "前往註冊",
                                            "uri" => "https://liff.line.me/2004625704-jDdyYEnk/register?ts=" . time()
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ];
                    LINEMessagingApi::replyMessage(new ReplyMessageRequest([
                        'replyToken' => $replyToken,
                        'messages' => [$flex]
                    ]));
                }
            }
        }
        
        // 確保返回 200 狀態碼
        return response('OK', 200);
    }
}