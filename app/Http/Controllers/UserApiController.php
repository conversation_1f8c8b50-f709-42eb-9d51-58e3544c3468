<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UserApiController extends Controller
{
    // 1. 檢查 line_user_id 與 vendor_id 是否存在，並檢查使用者資料完整性
    public function check(Request $request)
    {
        $request->validate([
            'line_user_id' => 'required|string',
            'vendor_id' => 'required|integer',
        ]);

        $vendor = Vendor::find($request->vendor_id);
        $user = User::where('line_user_id', $request->line_user_id)
            ->where('vendor_id', $request->vendor_id)
            ->first();

        $user_complete = false;
        if ($user) {
            $user_complete = $user->name && $user->email && $user->mobile;
        }

        return response()->json([
            'user_exists' => (bool)$user,
            'vendor_exists' => (bool)$vendor,
            'user_complete' => (bool)$user_complete,
            'user' => $user ? [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'mobile' => $user->mobile,
            ] : null,
        ]);
    }

    // 2. 註冊使用者資訊，主要回傳 vendor_id
    public function register(Request $request)
    {
        Log::info('register: ' . json_encode($request->all()));
        $request->validate([
            'line_user_id' => 'required|string',
            'vendor_id' => 'required|integer',
            'name' => 'required|string',
            'email' => 'required|email',
            'mobile' => 'required|string',
            'password' => 'required|string|min:6',
        ]);

        $vendor = Vendor::find($request->vendor_id);
        if (!$vendor) {
            return response()->json(['error' => 'Vendor not found'], 404);
        }

        try {
            $user = User::where('line_user_id', $request->line_user_id)
                ->where('vendor_id', $request->vendor_id)
                ->first();
            if ($user) {
                $user->update([
                    'name' => $request->name,
                    'email' => $request->email,
                    'mobile' => $request->mobile,
                ]);
            } else {
                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'mobile' => $request->mobile,
                    'line_user_id' => $request->line_user_id,
                    'vendor_id' => $request->vendor_id,
                    'password' => bcrypt($request->password),
                ]);
            }
        } catch (\Illuminate\Database\QueryException $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }

        return response()->json([
            'success' => true,
            'vendor_id' => $vendor->id,
            'user_id' => $user->id,
        ]);
    }

    /**
     * 取得該使用者所屬 vendor 的所有商品及最新價格
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getVendorProductsWithLatestPrice(Request $request)
    {
        $request->validate([
            'line_user_id' => 'required|string',
        ]);

        $user = User::where('line_user_id', $request->line_user_id)->first();
        if (!$user || !$user->vendor_id) {
            return response()->json(['error' => 'User or vendor not found'], 404);
        }

        $vendorId = $user->vendor_id;

        // 1. 找出該 vendor 所有商品的最新價格
        $latestPrices = \App\Models\ProductPrice::where('vendor_id', $vendorId)
            ->selectRaw('product_id, MAX(created_at) as latest_created_at')
            ->groupBy('product_id')
            ->pluck('latest_created_at', 'product_id');

        // 2. 取得這些最新價格的詳細資料
        $priceDetails = \App\Models\ProductPrice::where('vendor_id', $vendorId)
            ->whereIn('product_id', $latestPrices->keys())
            ->whereIn('created_at', $latestPrices->values())
            ->get()
            ->keyBy('product_id');

        // 3. 取得商品資料
        $products = \App\Models\Product::whereIn('id', $latestPrices->keys())->get();

        $result = $products->map(function($product) use ($priceDetails) {
            $price = $priceDetails->get($product->id);
            return [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'latest_price' => $price ? $price->price : null,
                'price_updated_at' => $price ? $price->created_at : null,
            ];
        });

        return response()->json([
            'vendor_id' => $vendorId,
            'products' => $result,
        ]);
    }

    /**
     * 只允許更新使用者名稱
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateUserName(Request $request)
    {
        $request->validate([
            'line_user_id' => 'required|string',
            'vendor_id' => 'required|integer',
            'name' => 'required|string',
        ]);

        $user = User::where('line_user_id', $request->line_user_id)
            ->where('vendor_id', $request->vendor_id)
            ->first();
        if (!$user) {
            return response()->json([
                'success' => false,
                'error' => 'User not found',
            ], 404);
        }

        try {
            $user->update([
                'name' => $request->name,
            ]);
        } catch (\Illuminate\Database\QueryException $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
            ], 400);
        }

        return response()->json([
            'success' => true,
            'user_id' => $user->id,
            'name' => $user->name,
        ]);
    }
} 