<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class OrderController extends Controller
{
    // 取得所有訂單
    public function index()
    {
        return response()->json(Order::with(['user:id,name', 'vendor:id,name', 'products' => function($q) {
            $q->withPivot(['quantity', 'price_per_unit']);
        }])->latest()->get());
    }

    // 新增訂單
    public function store(Request $request)
    {
        $data = $request->validate([
            'line_user_id' => 'required|string',
            'vendor_id' => 'required|exists:vendors,id',
            'total_amount' => 'required|numeric',
            'delivery_address' => 'required|string',
            'payment_method' => 'required|string',
            // 'payment_status' => 'required|string',
            'notes' => 'nullable|string',
            'order_items' => 'required|array|min:1',
            'order_items.*.product_id' => 'required|exists:products,id',
            'order_items.*.quantity' => 'required|integer|min:1',
            'order_items.*.price_per_unit' => 'required|numeric|min:0',
        ]);

        // 產生唯一訂單編號
        $orderNumber = 'ORD' . now()->format('YmdHis') . rand(1000, 9999);

        // 依 line_user_id 查 user_id
        $user = \App\Models\User::where('line_user_id', $data['line_user_id'])->first();
        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }

        DB::beginTransaction();
        try {
            $order = \App\Models\Order::create([
                'order_number' => $orderNumber,
                'user_id' => $user->id,
                'vendor_id' => $data['vendor_id'],
                'status' => 'pending',
                'total_amount' => $data['total_amount'],
                'delivery_address' => $data['delivery_address'],
                'payment_method' => $data['payment_method'],
                'payment_status' => 'unpaid',
                'notes' => $data['notes'] ?? null,
                'ordered_at' => now(),
            ]);
            foreach ($data['order_items'] as $item) {
                $order->products()->attach($item['product_id'], [
                    'quantity' => $item['quantity'],
                    'price_per_unit' => $item['price_per_unit'],
                ]);
            }
            DB::commit();
            // 發送 Slack 通知
            Http::post(config('services.slack.webhook_url'), [
                'text' => "新訂單通知：\n訂單編號：{$order->order_number}\n用戶ID：{$order->user_id}\n總金額：{$order->total_amount}"
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => $e->getMessage()], 500);
        }
        return response()->json($order->load(['user:id,name', 'vendor:id,name', 'products']), 201);
    }

    // 取得單一訂單
    public function show($id)
    {
        $order = Order::with(['user:id,name', 'vendor:id,name', 'products' => function($q) {
            $q->withPivot(['quantity', 'price_per_unit']);
        }])->findOrFail($id);
        return response()->json($order);
    }

    // 更新訂單
    public function update(Request $request, $id)
    {
        $order = Order::findOrFail($id);
        $data = $request->validate([
            'order_number' => 'sometimes|required|string|unique:orders,order_number,' . $id,
            'user_id' => 'sometimes|required|exists:users,id',
            'vendor_id' => 'sometimes|required|exists:vendors,id',
            'status' => 'sometimes|required|string',
            'total_amount' => 'sometimes|required|numeric',
            'delivery_address' => 'sometimes|required|string',
            'payment_method' => 'sometimes|required|string',
            'payment_status' => 'sometimes|required|string',
            'notes' => 'nullable|string',
            'ordered_at' => 'sometimes|required|date',
        ]);
        $order->update($data);
        return response()->json($order);
    }

    // 刪除訂單
    public function destroy($id)
    {
        $order = Order::findOrFail($id);
        $order->delete();
        return response()->json(['message' => 'deleted']);
    }
}
