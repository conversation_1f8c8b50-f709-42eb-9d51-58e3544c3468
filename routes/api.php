<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\LineWebhookController;
use App\Http\Controllers\UserApiController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::apiResource('orders', OrderController::class);

// LINE Bot Webhook 路由
Route::post('/line/webhook', [LineWebhookController::class, 'webhook']);

// 使用者相關 API
Route::post('/user/check', [UserApiController::class, 'check']);
Route::post('/user/register', [UserApiController::class, 'register']);
Route::post('/user/update-name', [UserApiController::class, 'updateUserName']);
Route::post('/user/products-with-latest-price', [UserApiController::class, 'getVendorProductsWithLatestPrice']);
