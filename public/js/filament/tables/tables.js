(()=>{var f=({canSelectMultipleRecords:d,canTrackDeselectedRecords:h,currentSelectionLivewireProperty:l,$wire:t})=>({checkboxClickController:null,collapsedGroups:[],isLoading:!1,selectedRecords:new Set,deselectedRecords:new Set,isTrackingDeselectedRecords:!1,shouldCheckUniqueSelection:!0,lastCheckedRecord:null,livewireId:null,entangledSelectedRecords:l?t.$entangle(l):null,init(){this.livewireId=this.$root.closest("[wire\\:id]").attributes["wire:id"].value,t.$on("deselectAllTableRecords",()=>this.deselectAllRecords()),l&&(d?this.selectedRecords=new Set(this.entangledSelectedRecords):this.selectedRecords=new Set(this.entangledSelectedRecords?[this.entangledSelectedRecords]:[])),this.$nextTick(()=>this.watchForCheckboxClicks()),Livewire.hook("element.init",({component:e})=>{e.id===this.livewireId&&this.watchForCheckboxClicks()})},mountAction(...e){t.set("isTrackingDeselectedTableRecords",this.isTrackingDeselectedRecords,!1),t.set("selectedTableRecords",[...this.selectedRecords],!1),t.set("deselectedTableRecords",[...this.deselectedRecords],!1),t.mountAction(...e)},toggleSelectRecordsOnPage(){let e=this.getRecordsOnPage();if(this.areRecordsSelected(e)){this.deselectRecords(e);return}this.selectRecords(e)},async toggleSelectRecordsInGroup(e){this.isLoading=!0,this.areRecordsSelected(this.getRecordsInGroupOnPage(e))?this.deselectRecords(await t.getGroupedSelectableTableRecordKeys(e)):this.selectRecords(await t.getGroupedSelectableTableRecordKeys(e)),this.isLoading=!1},getRecordsInGroupOnPage(e){let s=[];for(let i of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])i.dataset.group===e&&s.push(i.value);return s},getSelectedRecordsCount(){return this.isTrackingDeselectedRecords?(this.$refs.allSelectableRecordsCount?.value??this.deselectedRecords.size)-this.deselectedRecords.size:this.selectedRecords.size},getRecordsOnPage(){let e=[];for(let s of this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[])e.push(s.value);return e},selectRecords(e){d||(this.deselectAllRecords(),e=e.slice(0,1));for(let s of e)if(!this.isRecordSelected(s)){if(this.isTrackingDeselectedRecords){this.deselectedRecords.delete(s);continue}this.selectedRecords.add(s)}this.updatedSelectedRecords()},deselectRecords(e){for(let s of e){if(this.isTrackingDeselectedRecords){this.deselectedRecords.add(s);continue}this.selectedRecords.delete(s)}this.updatedSelectedRecords()},updatedSelectedRecords(){if(d){this.entangledSelectedRecords=[...this.selectedRecords];return}this.entangledSelectedRecords=[...this.selectedRecords][0]??null},toggleSelectedRecord(e){if(this.isRecordSelected(e)){this.deselectRecords([e]);return}this.selectRecords([e])},async selectAllRecords(){if(!h){this.isLoading=!0,this.selectedRecords=new Set(await t.getAllSelectableTableRecordKeys()),this.updatedSelectedRecords(),this.isLoading=!1;return}this.isTrackingDeselectedRecords=!0,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},deselectAllRecords(){this.isTrackingDeselectedRecords=!1,this.selectedRecords=new Set,this.deselectedRecords=new Set,this.updatedSelectedRecords()},isRecordSelected(e){return this.isTrackingDeselectedRecords?!this.deselectedRecords.has(e):this.selectedRecords.has(e)},areRecordsSelected(e){return e.every(s=>this.isRecordSelected(s))},toggleCollapseGroup(e){if(this.isGroupCollapsed(e)){this.collapsedGroups.splice(this.collapsedGroups.indexOf(e),1);return}this.collapsedGroups.push(e)},isGroupCollapsed(e){return this.collapsedGroups.includes(e)},resetCollapsedGroups(){this.collapsedGroups=[]},watchForCheckboxClicks(){this.checkboxClickController&&this.checkboxClickController.abort(),this.checkboxClickController=new AbortController;let{signal:e}=this.checkboxClickController;this.$root?.addEventListener("click",s=>s.target?.matches(".fi-ta-record-checkbox")&&this.handleCheckboxClick(s,s.target),{signal:e})},handleCheckboxClick(e,s){if(!this.lastChecked){this.lastChecked=s;return}if(e.shiftKey){let i=Array.from(this.$root?.getElementsByClassName("fi-ta-record-checkbox")??[]);if(!i.includes(this.lastChecked)){this.lastChecked=s;return}let r=i.indexOf(this.lastChecked),o=i.indexOf(s),n=[r,o].sort((c,g)=>c-g),a=[];for(let c=n[0];c<=n[1];c++)i[c].checked=s.checked,a.push(i[c].value);s.checked?this.selectRecords(a):this.deselectRecords(a)}this.lastChecked=s}});function u({columns:d,isLive:h}){return{error:void 0,isLoading:!1,columns:d,isLive:h,init(){if(!this.columns||this.columns.length===0){this.columns=[];return}},get groupedColumns(){let l={};return this.columns.filter(t=>t.type==="group").forEach(t=>{l[t.name]=this.calculateGroupedColumns(t)}),l},calculateGroupedColumns(l){if(!l?.columns)return{checked:!1,disabled:!0,indeterminate:!1};let t=l.columns.filter(i=>i.isToggleable!==!1);if(t.length===0)return{checked:!0,disabled:!0,indeterminate:!1};let e=t.filter(i=>i.isToggled).length,s=l.columns.filter(i=>i.isToggleable===!1);return e===0&&s.length>0?{checked:!0,disabled:!1,indeterminate:!0}:e===0?{checked:!1,disabled:!1,indeterminate:!1}:e===t.length?{checked:!0,disabled:!1,indeterminate:!1}:{checked:!0,disabled:!1,indeterminate:!0}},getColumn(l,t=null){return t?this.columns.find(s=>s.type==="group"&&s.name===t)?.columns?.find(s=>s.name===l):this.columns.find(e=>e.name===l)},toggleGroup(l){let t=this.columns.find(o=>o.type==="group"&&o.name===l);if(!t?.columns)return;let e=this.calculateGroupedColumns(t);if(e.disabled)return;let i=t.columns.filter(o=>o.isToggleable!==!1).some(o=>o.isToggled),r=e.indeterminate?!0:!i;t.columns.filter(o=>o.isToggleable!==!1).forEach(o=>{o.isToggled=r}),this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},toggleColumn(l,t=null){let e=this.getColumn(l,t);!e||e.isToggleable===!1||(e.isToggled=!e.isToggled,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager())},reorderColumns(l){let t=l.map(e=>e.split("::"));this.reorderTopLevel(t),this.isLive&&this.applyTableColumnManager()},reorderGroupColumns(l,t){let e=this.columns.find(r=>r.type==="group"&&r.name===t);if(!e)return;let s=l.map(r=>r.split("::")),i=[];s.forEach(([r,o])=>{let n=e.columns.find(a=>a.name===o);n&&i.push(n)}),e.columns=i,this.columns=[...this.columns],this.isLive&&this.applyTableColumnManager()},reorderTopLevel(l){let t=this.columns,e=[];l.forEach(([s,i])=>{let r=t.find(o=>s==="group"?o.type==="group"&&o.name===i:s==="column"?o.type!=="group"&&o.name===i:!1);r&&e.push(r)}),this.columns=e},async applyTableColumnManager(){this.isLoading=!0;try{await this.$wire.call("applyTableColumnManager",this.columns),this.error=void 0}catch(l){this.error="Failed to update column visibility",console.error("Table toggle columns error:",l)}finally{this.isLoading=!1}}}}document.addEventListener("alpine:init",()=>{window.Alpine.data("filamentTable",f),window.Alpine.data("filamentTableColumnManager",u)});})();
