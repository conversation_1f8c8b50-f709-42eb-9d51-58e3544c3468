<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Product>
 */
class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        $names = [
            '10KG 瓦斯',
            '20KG 瓦斯',
            '30KG 瓦斯',
        ];
        return [
            'name' => $this->faker->randomElement($names),
            'sku' => $this->faker->unique()->bothify('GAS-####'),
            'description' => $this->faker->sentence(),
            'image_url' => $this->faker->imageUrl(300, 300, 'business', true, 'gas'),
            'is_published' => true,
        ];
    }
} 