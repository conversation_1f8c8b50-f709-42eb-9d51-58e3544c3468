<?php

namespace Database\Factories;

use App\Models\Vendor;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Vendor>
 */
class VendorFactory extends Factory
{
    protected $model = Vendor::class;

    public function definition(): array
    {
        $names = [
            '台灣瓦斯行',
            '大同瓦斯行',
            '中油瓦斯專賣',
            '新北瓦斯配送',
            '高雄瓦斯行',
            '幸福瓦斯',
            '台中瓦斯專賣',
            '永和瓦斯行',
            '信義瓦斯',
            '三重瓦斯配送',
        ];
        return [
            'name' => $this->faker->unique()->randomElement($names),
            'address' => $this->faker->address(),
            'phone' => $this->faker->phoneNumber(),
            'is_active' => true,
        ];
    }
} 