<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Vendor;
use App\Models\Product;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        if (!\App\Models\User::where('email', '<EMAIL>')->exists()) {
            \App\Models\User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        Vendor::factory(10)->create();
        Product::factory(10)->create();
    }
}
