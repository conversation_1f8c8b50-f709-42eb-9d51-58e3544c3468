<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique()->comment('訂單編號');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            // 如果廠商被刪除，訂單應保留但廠商ID設為NULL，或限制刪除。這裡使用 restrict (限制) 更安全。
            $table->foreignId('vendor_id')->constrained()->onDelete('restrict');
            $table->string('status', 50)->default('pending')->comment('訂單狀態: pending, confirmed, delivering, completed, cancelled');
            $table->decimal('total_amount', 10, 2)->comment('訂單總金額');
            $table->text('delivery_address')->comment('地址快照');
            $table->string('payment_method', 50)->comment('付款方式');
            $table->string('payment_status', 50)->default('unpaid')->comment('付款狀態: unpaid, paid');
            $table->text('notes')->nullable()->comment('客戶備註');
            $table->timestamp('ordered_at')->useCurrent()->comment('下單時間');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
