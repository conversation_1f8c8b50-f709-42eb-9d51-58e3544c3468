<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('contact_name')->comment('聯絡人姓名');
            $table->string('contact_phone', 20)->comment('聯絡電話');
            $table->string('city', 50)->nullable()->comment('縣市');
            $table->string('district', 50)->nullable()->comment('鄉鎮市區');
            $table->string('zip_code', 10)->nullable()->comment('郵遞區號');
            $table->string('address')->comment('詳細地址');
            $table->boolean('is_default')->default(false)->comment('是否為預設地址');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('addresses');
    }
};
