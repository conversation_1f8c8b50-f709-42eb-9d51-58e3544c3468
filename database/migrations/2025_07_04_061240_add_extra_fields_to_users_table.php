<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('mobile', 20)->nullable()->after('email')->comment('手機號碼');

            // 新增 line_user_id，這是與 LINE 綁定的關鍵，應該是唯一的
            $table->string('line_user_id')->nullable()->unique()->after('mobile')->comment('LINE Platform User ID');

            // Laravel 預設的 name 欄位可以當作 nickname 使用，但如果想分開，可以新增 nickname
            // 如果您決定用現有的 'name' 欄位，就不需要下面這行
            $table->string('nickname')->nullable()->after('name')->comment('暱稱');
            
            // 新增 role 欄位，用來區分使用者角色 (例如: user, admin, vendor)
            $table->string('role', 50)->default('user')->after('line_user_id')->comment('使用者角色');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['mobile', 'line_user_id', 'nickname', 'role']);
        });
    }
};
