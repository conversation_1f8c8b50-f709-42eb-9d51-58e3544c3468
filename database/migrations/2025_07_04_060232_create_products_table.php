<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('商品名稱');
            $table->string('sku', 50)->nullable()->unique()->comment('商品貨號');
            $table->text('description')->nullable()->comment('商品描述');
            $table->string('image_url')->nullable()->comment('商品圖片URL');
            $table->boolean('is_published')->default(true)->comment('是否上架');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
